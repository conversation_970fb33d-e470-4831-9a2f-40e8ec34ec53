# 项目应付付款业务流程优化技术实现方案

## 1. 概述

基于业务需求分析，需要对现有的项目应付付款流程进行优化，主要解决税率信息来源不一致的问题，并支持三种不同的业务流程。

## 2. 核心修改点

### 2.1 新增核销检查服务

#### 2.1.1 创建核销检查服务接口

```java
package com.cloud.ficonsumer.service;

/**
 * 预付款核销检查服务
 */
public interface PrepaymentVerificationService {
  
    /**
     * 检查付款合同是否存在预付款
     * @param pkContr 付款合同主键
     * @return true-存在预付款，false-不存在预付款
     */
    boolean hasPrePayment(String pkContr);
  
    /**
     * 检查预付款是否已核销
     * @param pkContr 付款合同主键
     * @return true-已核销，false-未核销
     */
    boolean isPrePaymentVerified(String pkContr);
  
    /**
     * 获取预付款对应的虚拟订单单号
     * @param pkContr 付款合同主键
     * @return 虚拟订单单号
     */
    String getPrePaymentOrderNo(String pkContr);
  
    /**
     * 检查并获取预付款核销信息
     * @param pkContr 付款合同主键
     * @return 核销信息对象
     */
    PrepaymentVerificationResult checkPrePaymentVerification(String pkContr);
}
```

#### 2.1.2 核销信息结果对象

```java
package com.cloud.ficonsumer.vo;

import lombok.Data;

/**
 * 预付款核销检查结果
 */
@Data
public class PrepaymentVerificationResult {
  
    /**
     * 是否存在预付款
     */
    private boolean hasPrePayment;
  
    /**
     * 是否已核销
     */
    private boolean isVerified;
  
    /**
     * 预付款对应的虚拟订单单号
     */
    private String orderNo;
  
    /**
     * 预付款金额
     */
    private BigDecimal prePaymentAmount;
  
    /**
     * 核销详情
     */
    private String verificationDetails;
}
```

### 2.2 虚拟订单单号生成服务

#### 2.2.1 创建订单单号生成服务接口

```java
package com.cloud.ficonsumer.service;

/**
 * 虚拟订单单号生成服务
 */
public interface VirtualOrderNumberService {
  
    /**
     * 生成基于合同的虚拟订单单号（序列递增）
     * @param contractNo 合同单号
     * @return 虚拟订单单号
     */
    String generateOrderNumber(String contractNo);
  
    /**
     * 获取已存在的预付款订单单号
     * @param pkContr 付款合同主键
     * @return 已存在的订单单号，如果不存在返回null
     */
    String getExistingOrderNumber(String pkContr);
  
    /**
     * 判断是否需要复用订单单号
     * @param pkContr 付款合同主键
     * @return true-需要复用，false-生成新单号
     */
    boolean shouldReuseOrderNumber(String pkContr);
}
```

### 2.3 修改YgInvoiceCheckContext

#### 2.3.1 新增核销检查相关字段

```java
public class YgInvoiceCheckContext {
    // 现有字段...
  
    // 新增：预付款核销检查结果
    private PrepaymentVerificationResult verificationResult;
  
    // 新增：虚拟订单单号
    private String virtualOrderNumber;
  
    // 新增：是否为预付款冲抵流程
    private boolean isPrePaymentOffset = false;
  
    // 新增：业务流程类型
    private PaymentFlowType paymentFlowType;
  
    // 构建状态标记
    private boolean verificationChecked = false;
    private boolean orderNumberGenerated = false;
}
```

#### 2.3.2 新增业务流程类型枚举

```java
package com.cloud.ficonsumer.enums;

/**
 * 付款业务流程类型
 */
public enum PaymentFlowType {
    /**
     * 流程1：无预付款直接开票付款
     */
    DIRECT_INVOICE_PAYMENT("1", "直接开票付款"),
  
    /**
     * 流程2：预付款流程
     */
    PREPAYMENT("2", "预付款流程"),
  
    /**
     * 流程3：正式付款冲抵预付款
     */
    PREPAYMENT_OFFSET("3", "正式付款冲抵预付款");
  
    private final String code;
    private final String name;
  
    PaymentFlowType(String code, String name) {
        this.code = code;
        this.name = name;
    }
  
    public String getCode() {
        return code;
    }
  
    public String getName() {
        return name;
    }
}
```

#### 2.3.3 新增Builder方法

```java
public static class Builder {
    // 现有方法...
  
    /**
     * 执行预付款核销检查
     */
    public Builder withVerificationCheck() {
        if (!context.verificationChecked && context.isProjectPayable()) {
            context.performVerificationCheck();
            context.verificationChecked = true;
        }
        return this;
    }
  
    /**
     * 生成虚拟订单单号
     */
    public Builder withOrderNumberGeneration() {
        if (!context.orderNumberGenerated && context.isProjectPayable()) {
            context.generateVirtualOrderNumber();
            context.orderNumberGenerated = true;
        }
        return this;
    }
  
    /**
     * 确定业务流程类型
     */
    public Builder withFlowTypeDetection() {
        if (context.paymentFlowType == null && context.isProjectPayable()) {
            context.detectPaymentFlowType();
        }
        return this;
    }
}
```

### 2.4 修改FiSourcePuPayableServiceImpl

#### 2.4.1 修改push方法

```java
@Override
public boolean push(String pkPayablebill) {
    // 获取源数据
    FiSourcePuPayable source = this.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
            .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
    List<FiSourcePuPayableDetail> sourceDetails = detailService.list(Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
            .eq(FiSourcePuPayableDetail::getPkPayablebill, pkPayablebill));

    try {
        // 第一阶段：订单同步（仅项目应付需要）
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .build();

        // 若为项目应付，需要首先进行业务流程判断和核销检查
        if (context.isProjectPayable()) {
            context = context.continueWith()
                .withBasicInfo()                // 基础信息
                .withTopBill()                  // 上游单据信息（收票登记）
                .withSourceBill()               // 源头单据信息（付款合同或费用结算单）
                .withFlowTypeDetection()        // 新增：业务流程类型检测
                .withVerificationCheck()        // 新增：预付款核销检查
                .withOrderNumberGeneration()    // 新增：虚拟订单单号生成
                .build();

            log.info("开始同步订单，应付单类型: {}, 业务流程类型: {}, 单据号: {}",
                    context.getPayableTypeName(), 
                    context.getPaymentFlowType().getName(),
                    source.getBillno());

            // 使用Context进行fi_source_base_order的加工处理
            processBaseOrderSync(context);

            log.info("订单同步完成，单据号: {}", source.getBillno());
        }

        // 第二阶段：应付单转换数据
        log.info("开始转换应付单数据，单据号: {}", source.getBillno());
        convertData(source, sourceDetails);

        // 获取转换后的数据并更新到context中
        FiConvertPuPayable convert = convertService.getOne(Wrappers.<FiConvertPuPayable>lambdaQuery()
                .eq(FiConvertPuPayable::getPkPayablebill, source.getPkPayablebill()));
        List<FiConvertPuPayableDetail> convertDetails = convertDetailService.list(Wrappers.<FiConvertPuPayableDetail>lambdaQuery()
                .eq(FiConvertPuPayableDetail::getPkPayablebill, source.getPkPayablebill()));

        // 将转换数据添加到context中，继续构建
        context = context.continueWith()
                .withConvertData(convert, convertDetails)
                .build();

        log.info("应付单数据转换完成，单据号: {}", source.getBillno());

        // 第三阶段：应付单推送
        log.info("开始推送应付单，单据号: {}", source.getBillno());

        // 继续构建完整的context用于推送
        context = context.continueWith()
                .withCosTypeInfo()      // 费用类型信息
                .withInvoiceInfo()      // 发票信息
                .withAccountInfo()      // 账户信息
                .withImageInfo()        // 影像信息
                .withValidation()       // 业务校验
                .build();

        pushToYgPlatform(source, context);
        log.info("应付单推送完成，单据号: {}", source.getBillno());

    } catch (Exception e) {
        // 统一异常处理
        handleUnifiedException(source, e);
        throw new CheckedException("【采购-发票校验申请服务】处理失败:" + e.getMessage());
    }

    return true;
}
```

## 3. 数据库查询实现

### 3.1 新增Mapper方法

#### 3.1.1 在DmMapper中新增核销查询方法

```java
/**
 * 查询付款合同是否存在预付款
 */
@Select("SELECT COUNT(1) FROM ap_paybill " +
        "INNER JOIN ap_payitem ON ap_paybill.pk_paybill = ap_payitem.pk_paybill " +
        "   AND ap_payitem.dr = 0 " +
        "INNER JOIN pm_contr ON ap_payitem.src_billid = pm_contr.pk_contr " +
        "WHERE ap_payitem.prepay = 1 " +
        "  AND ap_paybill.pk_tradetype = 'F3-Cxx-01' " +
        "  AND ap_paybill.dr = 0 " +
        "  AND pm_contr.pk_contr = #{pkContr}")
int countPrePaymentByContract(@Param("pkContr") String pkContr);

/**
 * 查询预付款是否已核销
 */
@Select("SELECT COUNT(1) FROM ap_payablebill " +
        "INNER JOIN ap_payableitem ON ap_payablebill.pk_payablebill = ap_payableitem.pk_payablebill " +
        "   AND ap_payableitem.dr = 0 " +
        "INNER JOIN pm_contr ON ap_payableitem.src_billid = pm_contr.pk_contr " +
        "INNER JOIN arap_verifydetail ON arap_verifydetail.pk_bill = ap_payablebill.pk_payablebill " +
        "   AND arap_verifydetail.dr = 0 " +
        "INNER JOIN ap_paybill ON ap_paybill.pk_paybill = arap_verifydetail.pk_bill2 " +
        "INNER JOIN ap_payitem ON ap_payitem.pk_paybill = ap_paybill.pk_paybill " +
        "   AND ap_payitem.dr = 0 " +
        "WHERE ap_payablebill.pk_tradetype = 'F1-Cxx-01' " +
        "  AND ap_payablebill.dr = 0 " +
        "  AND ap_payitem.prepay = 1 " +
        "  AND pm_contr.pk_contr = #{pkContr}")
int countVerifiedPrePaymentByContract(@Param("pkContr") String pkContr);

/**
 * 获取预付款对应的虚拟订单单号
 */
@Select("SELECT fi_source_virtual_order.bill_code " +
        "FROM fi_source_virtual_order " +
        "WHERE fi_source_virtual_order.pk_contr = #{pkContr} " +
        "  AND fi_source_virtual_order.del_flag = 0 " +
        "ORDER BY fi_source_virtual_order.create_time DESC " +
        "LIMIT 1")
String getPrePaymentOrderNo(@Param("pkContr") String pkContr);
```

## 4. 服务实现类

### 4.1 核销检查服务实现

```java
@Service
@AllArgsConstructor
public class PrepaymentVerificationServiceImpl implements PrepaymentVerificationService {
  
    private final DmMapper dmMapper;
  
    @Override
    public boolean hasPrePayment(String pkContr) {
        return dmMapper.countPrePaymentByContract(pkContr) > 0;
    }
  
    @Override
    public boolean isPrePaymentVerified(String pkContr) {
        return dmMapper.countVerifiedPrePaymentByContract(pkContr) > 0;
    }
  
    @Override
    public String getPrePaymentOrderNo(String pkContr) {
        return dmMapper.getPrePaymentOrderNo(pkContr);
    }
  
    @Override
    public PrepaymentVerificationResult checkPrePaymentVerification(String pkContr) {
        PrepaymentVerificationResult result = new PrepaymentVerificationResult();
      
        // 检查是否存在预付款
        boolean hasPrePayment = hasPrePayment(pkContr);
        result.setHasPrePayment(hasPrePayment);
      
        if (hasPrePayment) {
            // 检查是否已核销
            boolean isVerified = isPrePaymentVerified(pkContr);
            result.setIsVerified(isVerified);
          
            if (isVerified) {
                // 获取订单单号
                String orderNo = getPrePaymentOrderNo(pkContr);
                result.setOrderNo(orderNo);
            }
        }
      
        return result;
    }
}
```

### 4.2 虚拟订单单号生成服务实现

```java
@Service
@AllArgsConstructor
public class VirtualOrderNumberServiceImpl implements VirtualOrderNumberService {
  
    private final DmMapper dmMapper;
    private final PrepaymentVerificationService verificationService;
  
    @Override
    public String generateOrderNumber(String contractNo) {
        // 生成基于合同单号的序列递增订单号
        // 格式：合同单号 + "-" + 序列号
        String sequence = generateSequence(contractNo);
        return contractNo + "-" + sequence;
    }
  
    @Override
    public String getExistingOrderNumber(String pkContr) {
        return verificationService.getPrePaymentOrderNo(pkContr);
    }
  
    @Override
    public boolean shouldReuseOrderNumber(String pkContr) {
        PrepaymentVerificationResult result = verificationService.checkPrePaymentVerification(pkContr);
        return result.isHasPrePayment() && result.isIsVerified();
    }
  
    private String generateSequence(String contractNo) {
        // 实现序列号生成逻辑
        // 可以基于数据库序列或者Redis计数器
        return String.format("%03d", getNextSequence(contractNo));
    }
  
    private int getNextSequence(String contractNo) {
        // 实现获取下一个序列号的逻辑
        // 这里可以使用数据库查询或Redis实现
        return 1; // 简化实现
    }
}
```

## 5. 异常处理增强

### 5.1 新增异常类型

```java
public enum PushStatusEnum {
    // 现有状态...
    VERIFY_FAIL(5, "核销检查失败"),
    ORDER_NUMBER_FAIL(6, "订单单号生成失败");
  
    // 现有代码...
}
```

### 5.2 修改异常处理方法

```java
private void handleUnifiedException(FiSourcePuPayable source, Exception e) {
    String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
    source.setPushTime(DateUtil.formatDateTime(new Date()));
    source.setIntegrationStatus("1");

    // 根据异常信息判断失败类型
    PushStatusEnum pushStatus;
    if (pushMsg.contains("订单同步失败")) {
        pushStatus = PushStatusEnum.ORDER_SYNC_FAIL;
    } else if (pushMsg.contains("转换失败")) {
        pushStatus = PushStatusEnum.CONVERT_FAIL;
    } else if (pushMsg.contains("核销检查失败") || pushMsg.contains("预付款未核销")) {
        pushStatus = PushStatusEnum.VERIFY_FAIL; // 新增
    } else if (pushMsg.contains("订单单号生成失败")) {
        pushStatus = PushStatusEnum.ORDER_NUMBER_FAIL; // 新增
    } else if (pushMsg.contains("推送失败")) {
        pushStatus = PushStatusEnum.PUSH_FAIL;
    } else {
        pushStatus = PushStatusEnum.PUSH_FAIL;
    }

    source.setPushStatus(pushStatus.getCode());
    source.setPushMsg(pushMsg);
    this.updateById(source);

    platformLogService.recordLog(
        source.getPkPayablebill(),
        source.getPkTradetype(),
        Constants.FAIL,
        pushMsg
    );
}
```

## 6. 实施步骤

### 6.1 第一阶段：基础服务实现

1. 创建PrepaymentVerificationService接口和实现类
2. 创建VirtualOrderNumberService接口和实现类
3. 新增相关的VO和枚举类
4. 在DmMapper中添加核销查询方法

### 6.2 第二阶段：Context增强

1. 修改YgInvoiceCheckContext，添加新字段和方法
2. 新增Builder方法支持核销检查和订单号生成
3. 实现业务流程类型检测逻辑

### 6.3 第三阶段：主流程修改

1. 修改FiSourcePuPayableServiceImpl的push方法
2. 增强异常处理机制
3. 添加详细的日志记录

### 6.4 第四阶段：测试验证

1. 编写单元测试
2. 进行集成测试
3. 业务场景验证
