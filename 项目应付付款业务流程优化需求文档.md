# 项目应付付款业务流程优化需求文档

## 1. 业务背景

### 1.1 问题描述

项目应付单、项目付款单源头订单来源于付款合同（pm_contr）和费用结算（pm_feebalance）。

由于付款合同中维护了税率信息，同时在项目应付单中传发票信息到业财库时，业财库计算税额、含税金额等相关字段时使用的税率会从合同上获取，而不是取发票的税率，导致了计算错误。

### 1.2 源头单据

- **付款合同（pm_contr）**：项目应付单和项目付款单的主要源头
- **费用结算（pm_feebalance）**：项目应付单的另一个源头

### 1.3 核心问题

税率信息来源不一致：

- 业财库使用：合同税率
- 实际应该使用：发票税率

## 2. 业务流程分析

### 2.1 修改后的三种业务流程

#### 2.1.1 流程1：无预付款直接开票付款

**适用场景**：没有针对合同的预付款，供应商直接开票付款

**业务流程**：

1. 供应商直接开票
2. 在收票登记节点维护票面信息
3. 票面信息关联到对应的付款合同
4. 虚拟订单需要取合同单号序列递增
5. 以票面信息作为订单传递到业财库

**关键特点**：

- 以票面信息为准
- 虚拟订单单号 = 合同单号 + 序列递增

#### 2.1.2 流程2：预付款流程

**适用场景**：针对合同的预付款，此时没有票面信息但需要付款

**业务流程**：

1. 从预付款单虚拟出一张订单
2. 订单单号取合同单号并序列递增
3. 传递虚拟订单到业财库

**关键特点**：

- 无票面信息
- 虚拟订单单号 = 合同单号 + 序列递增

#### 2.1.3 流程3：正式付款冲抵预付款

**适用场景**：存在预付款，在后续正式付款时，支付供应商开具的发票，该发票冲抵预付款金额

**业务流程**：

1. 供应商开具发票
2. 发票包含预付款金额，需要冲抵之前的预付款
3. 通过项目应付行的源头单据主键找到付款合同
4. 查询该付款合同是否存在预付款
5. 检查预付款是否已经核销
6. 如果未核销，抛出异常
7. 如果已核销，使用相同的订单单号
8. 用当前票的金额覆盖之前预付款传过去的订单

**关键特点**：

- 需要核销检查
- 使用相同订单单号
- 票面金额覆盖预付款金额

## 3. 核销逻辑分析

### 3.1 预付款SQL查询

```sql
-- 预付款查询
select *
from ap_paybill
inner join ap_payitem on ap_paybill.pk_paybill = ap_payitem.pk_paybill
   and ap_payitem.dr = 0
inner join pm_contr on ap_payitem.src_billid = pm_contr.pk_contr
where ap_payitem.prepay = 1 
  and ap_paybill.pk_tradetype = 'F3-Cxx-01'
  and ap_paybill.dr = 0
```

### 3.2 应付款未核销SQL查询

```sql
-- 应付款未核销查询
select *
from ap_payablebill
inner join ap_payableitem on ap_payablebill.pk_payablebill = ap_payableitem.pk_payablebill
   and ap_payableitem.dr = 0
inner join pm_contr on ap_payableitem.src_billid = pm_contr.pk_contr
where ap_payablebill.pk_tradetype = 'F1-Cxx-01'
  and ap_payablebill.dr = 0
  and not exists (
    select 1 from arap_verifydetail 
    where ap_payablebill.pk_payablebill = arap_verifydetail.pk_bill
  )
```

### 3.3 应付款已核销SQL查询

```sql
-- 应付款已核销查询
select ap_payablebill.*
from ap_payablebill
inner join ap_payableitem on ap_payablebill.pk_payablebill = ap_payableitem.pk_payablebill
   and ap_payableitem.dr = 0
inner join pm_contr on ap_payableitem.src_billid = pm_contr.pk_contr
inner join arap_verifydetail on arap_verifydetail.pk_bill = ap_payablebill.pk_payablebill
   and arap_verifydetail.dr = 0
inner join ap_paybill on ap_paybill.pk_paybill = arap_verifydetail.pk_bill2
inner join ap_payitem on ap_payitem.pk_paybill = ap_paybill.pk_paybill
   and ap_payitem.dr = 0
where ap_payablebill.pk_tradetype = 'F1-Cxx-01'
  and ap_payablebill.dr = 0
  and ap_payitem.prepay = 1
```

## 4. 技术实现方案

### 4.1 现有代码结构分析

#### 4.1.1 核心类结构

- **YgInvoiceCheckContext**：发票校验上下文，支持Builder模式构建
- **PayableType**：应付单类型枚举（采购应付、项目应付）
- **FiSourcePuPayableServiceImpl**：应付单服务实现类

#### 4.1.2 当前处理流程

```java
// 当前的三阶段处理流程
// 第一阶段：订单同步（仅项目应付需要）
// 第二阶段：应付单转换数据  
// 第三阶段：应付单推送
```

### 4.2 需要修改的核心逻辑

#### 4.2.1 虚拟订单单号生成规则

**当前逻辑**：使用固定规则生成
**修改后逻辑**： 

- 流程1和流程2：合同单号 + 序列递增
- 流程3：查找已存在的预付款订单单号，使用相同单号

#### 4.2.2 核销状态检查

**新增逻辑**：

1. 通过项目应付行的源头单据主键找到付款合同
2. 查询该付款合同是否存在预付款
3. 检查预付款核销状态
4. 未核销时抛出异常

#### 4.2.3 订单覆盖逻辑

**新增逻辑**：

- 流程3中，使用当前票面金额覆盖之前预付款的订单金额
- 保持订单单号不变

## 5. 数据库表结构

### 5.1 核心表说明

#### 5.1.1 应付单相关表

- **ap_payablebill**：应付单主表
- **ap_payableitem**：应付单明细表

#### 5.1.2 付款单相关表

- **ap_paybill**：付款单主表
- **ap_payitem**：付款单明细表

#### 5.1.3 核销表

- **arap_verifydetail**：核销明细表
  - pk_bill：应付单主键
  - pk_bill2：付款单主键

#### 5.1.4 合同表

- **pm_contr**：付款合同表
  - pk_contr：合同主键
  - bill_code：合同单号

### 5.2 关键字段说明

- **prepay**：付款性质（1-预付款，0-应付款）
- **pk_tradetype**：交易类型
  - F1-Cxx-01：项目应付单
  - F3-Cxx-01：项目付款单
- **src_billid**：源头单据主键

## 6. 实施计划

### 6.1 第一阶段：核销逻辑实现

1. 实现预付款查询逻辑
2. 实现核销状态检查逻辑
3. 添加异常处理机制

### 6.2 第二阶段：订单单号生成优化

1. 修改虚拟订单单号生成规则
2. 实现合同单号序列递增逻辑
3. 实现订单单号复用逻辑

### 6.3 第三阶段：订单覆盖逻辑

1. 实现订单金额覆盖逻辑
2. 完善业务流程控制
3. 添加日志记录

### 6.4 第四阶段：测试验证

1. 单元测试
2. 集成测试
3. 业务场景测试

## 7. 风险评估

### 7.1 技术风险

- 核销逻辑复杂，需要仔细处理各种边界情况
- 订单单号生成规则变更可能影响现有业务

### 7.2 业务风险

- 三种流程的判断逻辑需要准确无误
- 核销状态检查的时机需要合适

### 7.3 数据风险

- 历史数据的兼容性处理
- 并发情况下的数据一致性

## 8. 详细技术实现

### 8.1 核心业务流程时序图

#### 8.1.1 流程3：正式付款冲抵预付款时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Service as FiSourcePuPayableService
    participant Context as YgInvoiceCheckContext
    participant Mapper as DmMapper
    participant VerifyService as 核销检查服务

    Client->>Service: push(pkPayablebill)
    Service->>Context: 构建Context
    Context->>Mapper: 查询付款合同信息
    Mapper-->>Context: 返回合同信息

    Service->>VerifyService: 检查是否存在预付款
    VerifyService->>Mapper: 查询预付款SQL
    Mapper-->>VerifyService: 返回预付款信息

    alt 存在预付款
        VerifyService->>Mapper: 查询核销状态SQL
        Mapper-->>VerifyService: 返回核销状态

        alt 未核销
            VerifyService-->>Service: 抛出异常
        else 已核销
            VerifyService->>Service: 获取原订单单号
            Service->>Service: 使用相同单号覆盖金额
        end
    else 无预付款
        Service->>Service: 生成新订单单号
    end

    Service->>Context: 推送到业财库
    Context-->>Service: 返回推送结果
    Service-->>Client: 返回处理结果
```

### 8.2 关键代码实现

#### 8.2.1 核销检查服务接口

```java
public interface PrepaymentVerificationService {
    /**
     * 检查付款合同是否存在预付款
     */
    boolean hasPrePayment(String pkContr);

    /**
     * 检查预付款是否已核销
     */
    boolean isPrePaymentVerified(String pkContr);

    /**
     * 获取预付款对应的订单单号
     */
    String getPrePaymentOrderNo(String pkContr);
}
```

#### 8.2.2 虚拟订单单号生成服务

```java
public interface VirtualOrderNumberService {
    /**
     * 生成基于合同的虚拟订单单号
     */
    String generateOrderNumber(String contractNo);

    /**
     * 获取已存在的预付款订单单号
     */
    String getExistingOrderNumber(String pkContr);
}
```

### 8.3 数据库查询优化

#### 8.3.1 核销状态查询索引建议

```sql
-- 建议在arap_verifydetail表上创建索引
CREATE INDEX idx_arap_verify_pk_bill ON arap_verifydetail(pk_bill);
CREATE INDEX idx_arap_verify_pk_bill2 ON arap_verifydetail(pk_bill2);

-- 建议在ap_payitem表上创建索引
CREATE INDEX idx_payitem_src_prepay ON ap_payitem(src_billid, prepay);
```

#### 8.3.2 查询性能优化

- 使用EXISTS替代IN子查询
- 合理使用表连接顺序
- 添加必要的过滤条件

## 9. 异常处理机制

### 9.1 异常分类

1. **订单同步失败**：上游订单同步到业财库失败
2. **应付转换失败**：应付单数据转换失败
3. **应付推送失败**：应付单推送到业财库失败
4. **核销检查失败**：预付款核销状态检查失败

### 9.2 统一异常处理

```java
private void handleUnifiedException(FiSourcePuPayable source, Exception e) {
    String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
    source.setPushTime(DateUtil.formatDateTime(new Date()));
    source.setIntegrationStatus("1");

    // 根据异常信息判断失败类型
    PushStatusEnum pushStatus;
    if (pushMsg.contains("订单同步失败")) {
        pushStatus = PushStatusEnum.ORDER_SYNC_FAIL;
    } else if (pushMsg.contains("转换失败")) {
        pushStatus = PushStatusEnum.CONVERT_FAIL;
    } else if (pushMsg.contains("核销检查失败")) {
        pushStatus = PushStatusEnum.VERIFY_FAIL; // 新增
    } else if (pushMsg.contains("推送失败")) {
        pushStatus = PushStatusEnum.PUSH_FAIL;
    } else {
        pushStatus = PushStatusEnum.PUSH_FAIL;
    }

    source.setPushStatus(pushStatus.getCode());
    source.setPushMsg(pushMsg);
    this.updateById(source);
}
```

## 10. 测试用例设计

### 10.1 流程1测试用例

- **用例1.1**：无预付款，直接开票付款
- **用例1.2**：收票登记信息完整性验证
- **用例1.3**：虚拟订单单号生成验证

### 10.2 流程2测试用例

- **用例2.1**：预付款单虚拟订单生成
- **用例2.2**：无票面信息处理验证
- **用例2.3**：预付款订单推送验证

### 10.3 流程3测试用例

- **用例3.1**：预付款已核销，正常覆盖
- **用例3.2**：预付款未核销，抛出异常
- **用例3.3**：订单单号复用验证
- **用例3.4**：金额覆盖逻辑验证

### 10.4 边界条件测试

- **用例4.1**：合同不存在的情况
- **用例4.2**：多个预付款的情况
- **用例4.3**：并发处理的情况
- **用例4.4**：数据不一致的情况

## 11. 验收标准

### 11.1 功能验收

- [ ] 三种业务流程能够正确识别和处理
- [ ] 核销状态检查逻辑正确
- [ ] 订单单号生成规则符合要求
- [ ] 订单覆盖逻辑正确执行
- [ ] 异常处理机制完善

### 11.2 性能验收

- [ ] 核销查询性能满足要求（<500ms）
- [ ] 整体处理时间不超过现有基准
- [ ] 并发处理能力满足要求

### 11.3 稳定性验收

- [ ] 异常情况处理完善
- [ ] 日志记录完整
- [ ] 回滚机制可靠
- [ ] 数据一致性保证

### 11.4 业务验收

- [ ] 税率计算准确性提升
- [ ] 业务流程符合实际需求
- [ ] 用户操作体验良好
